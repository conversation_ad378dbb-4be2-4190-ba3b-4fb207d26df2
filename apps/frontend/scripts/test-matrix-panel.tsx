import React from 'react';
import { render, screen, fireEvent } from '@testing-library/react';
import FeatureB1_1 from '../../../frontend/features/feature/featureB1_1';

// 模拟 Zustand store
jest.mock('../../../frontend/Store/store', () => ({
  useMatrixStore: () => ({
    coordinateButtonState: false,
    setCoordinateButtonState: jest.fn(),
    triggerInitialization: jest.fn(),
  }),
}));

describe('FeatureB1_1 矩阵功能面板', () => {
  test('应该正确渲染矩阵面板', () => {
    render(<FeatureB1_1 />);
    
    // 检查标题文本
    expect(screen.getByText('矩阵')).toBeInTheDocument();
    
    // 检查初始化按键
    expect(screen.getByText('初始化')).toBeInTheDocument();
    
    // 检查坐标按键
    expect(screen.getByText('坐标')).toBeInTheDocument();
  });

  test('应该有正确的样式', () => {
    const { container } = render(<FeatureB1_1 />);
    const mainDiv = container.firstChild as HTMLElement;
    
    // 检查主容器样式
    expect(mainDiv).toHaveStyle({
      position: 'relative',
      height: '15%',
      width: '94%',
      backgroundColor: '#bebebe',
      overflow: 'hidden',
      top: '6%'
    });
  });

  test('按键应该可以点击', () => {
    render(<FeatureB1_1 />);
    
    const initButton = screen.getByText('初始化');
    const coordButton = screen.getByText('坐标');
    
    // 测试按键是否可以点击
    fireEvent.click(initButton);
    fireEvent.click(coordButton);
    
    // 如果没有错误抛出，说明点击事件正常
    expect(initButton).toBeInTheDocument();
    expect(coordButton).toBeInTheDocument();
  });
});

console.log('矩阵功能面板测试脚本已创建');
