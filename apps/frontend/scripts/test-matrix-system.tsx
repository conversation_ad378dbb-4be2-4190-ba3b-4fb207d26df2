/**
 * 矩阵系统测试脚本
 * 用于验证三级容器矩阵系统的基本功能
 */

import React from 'react';
import { render, screen, fireEvent } from '@testing-library/react';
import FeatureA1 from '../../../frontend/features/feature/featureA1';
import FeatureA1Layout from '../../../frontend/features/feature/featureA1_layout';
import LogicA1 from '../../../frontend/features/logic/logicA1';

// 模拟Zustand store
jest.mock('../../../frontend/Store/store', () => ({
  useMatrixStore: () => ({
    coordinateButtonActive: false,
    initializationTimestamp: 0,
    modeActive: true,
  }),
  useMatrixComponentStore: () => ({
    components: {},
    setComponentActivated: jest.fn(),
    setComponentHighlighted: jest.fn(),
    clearAllHighlights: jest.fn(),
    resetAllComponents: jest.fn(),
  }),
}));

describe('矩阵系统测试', () => {
  test('FeatureA1组件应该正确渲染', () => {
    render(<FeatureA1 id="0-0" row={0} col={0} />);
    
    // 验证组件是否渲染
    const component = screen.getByRole('button', { hidden: true });
    expect(component).toBeInTheDocument();
  });

  test('FeatureA1Layout应该生成33x33网格', () => {
    render(<FeatureA1Layout />);
    
    // 验证网格容器存在
    const gridContainer = document.querySelector('[style*="grid-template-rows: repeat(33, 1fr)"]');
    expect(gridContainer).toBeInTheDocument();
  });

  test('LogicA1组件应该正确渲染', () => {
    render(<LogicA1 />);
    
    // 验证逻辑组件渲染
    const logicContainer = document.querySelector('[style*="position: relative"]');
    expect(logicContainer).toBeInTheDocument();
  });

  test('组件点击功能测试', () => {
    const mockSetComponentActivated = jest.fn();
    const mockSetComponentHighlighted = jest.fn();
    
    // 重新模拟store以包含mock函数
    jest.doMock('../../../frontend/Store/store', () => ({
      useMatrixComponentStore: () => ({
        components: { '0-0': { activated: false, highlighted: false } },
        setComponentActivated: mockSetComponentActivated,
        setComponentHighlighted: mockSetComponentHighlighted,
        clearAllHighlights: jest.fn(),
        resetAllComponents: jest.fn(),
      }),
    }));

    render(<FeatureA1 id="0-0" row={0} col={0} />);
    
    const component = screen.getByRole('button', { hidden: true });
    fireEvent.click(component);
    
    // 验证点击事件是否触发相应函数
    expect(mockSetComponentActivated).toHaveBeenCalled();
    expect(mockSetComponentHighlighted).toHaveBeenCalled();
  });
});

console.log('矩阵系统测试脚本已创建，包含以下测试用例：');
console.log('1. FeatureA1组件渲染测试');
console.log('2. FeatureA1Layout网格生成测试');
console.log('3. LogicA1组件渲染测试');
console.log('4. 组件点击功能测试');
console.log('');
console.log('运行测试命令: npm test test-matrix-system.tsx');
