'use client'

import React, { useState, useEffect } from 'react';
import { useStore } from '../../Store/store';
import FeatureA1Layout from '../feature/featureA1_layout';

const LogicA1: React.FC = () => {
  // 组件状态：33x33的网格
  const [componentStates, setComponentStates] = useState<{
    isActive: boolean;
    hasHighlight: boolean;
  }[][]>(() => {
    // 初始化33x33的状态数组
    return Array.from({ length: 33 }, () =>
      Array.from({ length: 33 }, () => ({
        isActive: false,
        hasHighlight: false,
      }))
    );
  });

  // 鼠标悬停状态
  const [hoveredComponent, setHoveredComponent] = useState<{ row: number; col: number } | null>(null);

  // 从store获取状态
  const { 
    modeButtonState, 
    coordinateButtonState, 
    initializeButtonTimestamp,
    activatedComponentCoordinates 
  } = useStore();

  // 监听初始化按键
  useEffect(() => {
    if (initializeButtonTimestamp > 0) {
      // 初始化所有组件状态
      setComponentStates(prev => 
        prev.map(row => 
          row.map(() => ({
            isActive: false,
            hasHighlight: false,
          }))
        )
      );
    }
  }, [initializeButtonTimestamp]);

  // 监听模式按键状态
  useEffect(() => {
    if (!modeButtonState) {
      // 模式为false时，禁用逻辑，清除所有状态
      setComponentStates(prev => 
        prev.map(row => 
          row.map(() => ({
            isActive: false,
            hasHighlight: false,
          }))
        )
      );
    } else {
      // 模式为true时，从store恢复激活的组件状态
      if (activatedComponentCoordinates.length > 0) {
        setComponentStates(prev => {
          const newStates = prev.map(row => row.map(cell => ({ ...cell })));
          
          activatedComponentCoordinates.forEach(coord => {
            if (coord.row >= 0 && coord.row < 33 && coord.col >= 0 && coord.col < 33) {
              newStates[coord.row][coord.col].isActive = true;
            }
          });
          
          return newStates;
        });
      }
    }
  }, [modeButtonState, activatedComponentCoordinates]);

  // 组件点击处理
  const handleComponentClick = (row: number, col: number) => {
    if (!modeButtonState) return; // 模式为false时不响应点击

    setComponentStates(prev => {
      const newStates = prev.map(r => r.map(c => ({ ...c })));
      
      // 激活状态切换（独立开关）
      newStates[row][col].isActive = !newStates[row][col].isActive;
      
      // 高亮边框（互斥单选）- 移除其他组件的高亮边框
      for (let r = 0; r < 33; r++) {
        for (let c = 0; c < 33; c++) {
          newStates[r][c].hasHighlight = false;
        }
      }
      // 设置当前组件的高亮边框
      newStates[row][col].hasHighlight = true;
      
      return newStates;
    });

    // 更新store中的激活组件坐标
    const currentActivated = componentStates
      .flatMap((row, rowIndex) => 
        row.map((cell, colIndex) => ({ row: rowIndex, col: colIndex, isActive: cell.isActive }))
      )
      .filter(item => item.isActive)
      .map(item => ({ row: item.row, col: item.col }));
    
    // 添加或移除当前点击的组件
    const isCurrentlyActive = componentStates[row][col].isActive;
    let updatedCoordinates: { row: number; col: number }[];
    
    if (isCurrentlyActive) {
      // 如果当前是激活状态，点击后变为未激活，从列表中移除
      updatedCoordinates = currentActivated.filter(coord => !(coord.row === row && coord.col === col));
    } else {
      // 如果当前是未激活状态，点击后变为激活，添加到列表中
      updatedCoordinates = [...currentActivated, { row, col }];
    }
    
    useStore.getState().setActivatedComponentCoordinates(updatedCoordinates);
  };

  // 鼠标悬停处理
  const handleComponentMouseEnter = (row: number, col: number) => {
    setHoveredComponent({ row, col });
  };

  const handleComponentMouseLeave = () => {
    setHoveredComponent(null);
  };

  // 如果模式按键为false，不渲染组件
  if (!modeButtonState) {
    return null;
  }

  return (
    <FeatureA1Layout
      componentStates={componentStates}
      showCoordinates={coordinateButtonState}
      hoveredComponent={hoveredComponent}
      onComponentClick={handleComponentClick}
      onComponentMouseEnter={handleComponentMouseEnter}
      onComponentMouseLeave={handleComponentMouseLeave}
    />
  );
};

export default LogicA1;
