'use client'

import React, { useEffect, useState } from 'react';
import { useMatrixStore, useMatrixComponentStore } from '../../Store/store';
import FeatureA1Layout from '../feature/featureA1_layout';

const LogicA1: React.FC = () => {
  const { 
    coordinateButtonActive, 
    initializationTimestamp, 
    modeActive 
  } = useMatrixStore();
  
  const { 
    clearAllHighlights, 
    resetAllComponents 
  } = useMatrixComponentStore();

  const [coordinateTexts, setCoordinateTexts] = useState<{ [key: string]: string }>({});

  // 监听模式按键状态
  useEffect(() => {
    if (!modeActive) {
      // 模式为false时，禁用逻辑，重置所有组件状态
      resetAllComponents();
      clearAllHighlights();
      setCoordinateTexts({});
    }
  }, [modeActive, resetAllComponents, clearAllHighlights]);

  // 监听初始化事件
  useEffect(() => {
    if (initializationTimestamp > 0) {
      // 初始化事件触发时，重置所有组件状态
      resetAllComponents();
      clearAllHighlights();
      setCoordinateTexts({});
    }
  }, [initializationTimestamp, resetAllComponents, clearAllHighlights]);

  // 监听坐标按键状态
  useEffect(() => {
    if (modeActive) {
      if (coordinateButtonActive) {
        // 计算并显示坐标
        const newCoordinateTexts: { [key: string]: string } = {};
        const centerRow = 16; // 33/2 = 16.5，取16作为中心
        const centerCol = 16;
        
        for (let row = 0; row < 33; row++) {
          for (let col = 0; col < 33; col++) {
            const id = `${row}-${col}`;
            const x = col - centerCol;
            const y = centerRow - row; // 反转Y轴，使上方为正
            newCoordinateTexts[id] = `${x},${y}`;
          }
        }
        setCoordinateTexts(newCoordinateTexts);
      } else {
        // 隐藏坐标
        setCoordinateTexts({});
      }
    }
  }, [coordinateButtonActive, modeActive]);

  // 如果模式未激活，不渲染组件
  if (!modeActive) {
    return null;
  }

  return (
    <div style={{ width: '100%', height: '100%', position: 'relative' }}>
      <FeatureA1Layout />
      
      {/* 坐标文本覆盖层 */}
      {coordinateButtonActive && (
        <div style={{
          position: 'absolute',
          top: 0,
          left: 0,
          width: '100%',
          height: '100%',
          display: 'grid',
          gridTemplateRows: 'repeat(33, 1fr)',
          gridTemplateColumns: 'repeat(33, 1fr)',
          gap: '5px',
          pointerEvents: 'none',
          zIndex: 5
        }}>
          {Object.entries(coordinateTexts).map(([id, text]) => (
            <div
              key={`coord-${id}`}
              style={{
                display: 'flex',
                justifyContent: 'center',
                alignItems: 'center',
                fontSize: '8px',
                color: '#242424',
                fontWeight: 'bold'
              }}
            >
              {text}
            </div>
          ))}
        </div>
      )}
    </div>
  );
};

export default LogicA1;
