'use client'

import React from 'react';

interface FeatureA1Props {
  isActive: boolean;
  hasHighlight: boolean;
  coordinateText: string;
  showCoordinates: boolean;
  onClick: () => void;
  onMouseEnter: () => void;
  onMouseLeave: () => void;
}

const FeatureA1: React.FC<FeatureA1Props> = ({
  isActive,
  hasHighlight,
  coordinateText,
  showCoordinates,
  onClick,
  onMouseEnter,
  onMouseLeave,
}) => {
  return (
    <div
      style={{
        // 组件形状: 方形，组件尺寸: 自动确定
        aspectRatio: '1',
        // 组件圆角: 5px
        borderRadius: '5px',
        // 展示方式: 弹性布局，弹性方向: 垂直
        display: 'flex',
        flexDirection: 'column',
        // 对齐方式: 水平，垂直居中
        justifyContent: 'center',
        alignItems: 'center',
        // 溢出处理: 隐藏
        overflow: 'hidden',
        // 组件定位: 相对定位
        position: 'relative',
        // 鼠标指针: 手型
        cursor: 'pointer',
        // 背景颜色根据激活状态变化
        backgroundColor: isActive ? '#929292' : '#ffffff',
        // 高亮边框样式
        border: hasHighlight ? '3px solid #ffd500' : '3px solid transparent',
        // 避免布局偏移 - 边框不影响元素尺寸
        boxSizing: 'border-box',
        // 过渡效果
        transition: 'all 0.2s ease',
      }}
      onClick={onClick}
      onMouseEnter={onMouseEnter}
      onMouseLeave={onMouseLeave}
    >
      {/* 坐标文本显示 */}
      {showCoordinates && (
        <span
          style={{
            // 字体大小: 8px
            fontSize: '8px',
            // 字体颜色: #242424
            color: '#242424',
            // 字体对齐: 居中
            textAlign: 'center',
            // 确保文本不会被选中
            userSelect: 'none',
          }}
        >
          {coordinateText}
        </span>
      )}
    </div>
  );
};

export default FeatureA1;
