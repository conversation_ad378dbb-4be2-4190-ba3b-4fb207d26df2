'use client'

import React from 'react';
import { useMatrixComponentStore } from '../../Store/store';

interface FeatureA1Props {
  id: string;
  row: number;
  col: number;
}

const FeatureA1: React.FC<FeatureA1Props> = ({ id, row, col }) => {
  const { components, setComponentActivated, setComponentHighlighted } = useMatrixComponentStore();
  
  // 获取当前组件状态
  const componentState = components[id] || { activated: false, highlighted: false };
  
  const handleClick = () => {
    // 切换激活状态（独立开关）
    setComponentActivated(id, !componentState.activated);
    
    // 设置高亮边框（互斥单选）
    setComponentHighlighted(id);
  };

  return (
    <div
      onClick={handleClick}
      style={{
        width: '100%',
        height: '100%',
        backgroundColor: componentState.activated ? '#929292' : '#ffffff',
        borderRadius: '5px',
        display: 'flex',
        flexDirection: 'column',
        justifyContent: 'center',
        alignItems: 'center',
        overflow: 'hidden',
        position: 'relative',
        cursor: 'pointer',
        fontSize: '8px',
        color: '#242424',
        textAlign: 'center',
        border: componentState.highlighted ? '3px solid #ffd500' : '3px solid transparent',
        boxSizing: 'border-box',
        transition: 'transform 0.2s ease',
      }}
      onMouseEnter={(e) => {
        e.currentTarget.style.transform = 'scale(1.2)';
        e.currentTarget.style.zIndex = '10';
      }}
      onMouseLeave={(e) => {
        e.currentTarget.style.transform = 'scale(1)';
        e.currentTarget.style.zIndex = '1';
      }}
    >
      {/* 坐标文本将由logicA1.tsx控制显示 */}
    </div>
  );
};

export default FeatureA1;
