'use client';

import React from 'react';
import { SecondaryButton } from '../../ButtonCodes/secondary/SecondaryButton/SecondaryButton';
import { useMatrixStore } from '../../Store/store';

const FeatureB1_1: React.FC = () => {
  const { 
    coordinateButtonState, 
    setCoordinateButtonState, 
    triggerInitialization 
  } = useMatrixStore();

  // 处理初始化按键点击
  const handleInitializationClick = () => {
    triggerInitialization();
    setCoordinateButtonState(false); // 重置坐标按键状态为false
  };

  // 处理坐标按键切换
  const handleCoordinateToggle = (isActive: boolean) => {
    setCoordinateButtonState(isActive);
  };

  return (
    <div style={{
      position: 'relative',
      height: '15%',
      width: '94%',
      backgroundColor: '#bebebe',
      overflow: 'hidden',
      top: '6%'
    }}>
      {/* 矩阵标题文本 */}
      <div style={{
        position: 'absolute',
        top: '20%',
        left: '1%',
        fontSize: '25px',
        color: '#242424'
      }}>
        矩阵
      </div>

      {/* 初始化按键 */}
      <div style={{
        position: 'absolute',
        top: '55%',
        left: '4%',
        height: '35%',
        width: '44%'
      }}>
        <SecondaryButton
          text="初始化"
          mode="instant"
          onClick={handleInitializationClick}
          style={{
            height: '100%',
            width: '100%',
            fontSize: 'inherit'
          }}
        />
      </div>

      {/* 坐标按键 */}
      <div style={{
        position: 'absolute',
        top: '55%',
        right: '4%',
        height: '35%',
        width: '44%'
      }}>
        <SecondaryButton
          text="坐标"
          mode="toggle"
          isActive={coordinateButtonState}
          onToggle={handleCoordinateToggle}
          style={{
            height: '100%',
            width: '100%',
            fontSize: 'inherit'
          }}
        />
      </div>
    </div>
  );
};

export default FeatureB1_1;
