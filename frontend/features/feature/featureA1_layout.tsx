'use client'

import React from 'react';
import FeatureA1 from './featureA1';

const FeatureA1Layout: React.FC = () => {
  // 生成33x33的网格组件
  const generateGridComponents = () => {
    const components = [];
    for (let row = 0; row < 33; row++) {
      for (let col = 0; col < 33; col++) {
        const id = `${row}-${col}`;
        components.push(
          <FeatureA1 
            key={id} 
            id={id}
            row={row}
            col={col}
          />
        );
      }
    }
    return components;
  };

  return (
    <div style={{
      width: '99%',
      height: '99%',
      display: 'grid',
      gridTemplateRows: 'repeat(33, 1fr)',
      gridTemplateColumns: 'repeat(33, 1fr)',
      gap: '5px',
      overflow: 'hidden'
    }}>
      {generateGridComponents()}
    </div>
  );
};

export default FeatureA1Layout;
