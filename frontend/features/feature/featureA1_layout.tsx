'use client'

import React from 'react';
import FeatureA1 from './featureA1';

interface FeatureA1LayoutProps {
  componentStates: {
    isActive: boolean;
    hasHighlight: boolean;
  }[][];
  showCoordinates: boolean;
  hoveredComponent: { row: number; col: number } | null;
  onComponentClick: (row: number, col: number) => void;
  onComponentMouseEnter: (row: number, col: number) => void;
  onComponentMouseLeave: () => void;
}

const FeatureA1Layout: React.FC<FeatureA1LayoutProps> = ({
  componentStates,
  showCoordinates,
  hoveredComponent,
  onComponentClick,
  onComponentMouseEnter,
  onComponentMouseLeave,
}) => {
  // 计算坐标，以中心组件为原点 (0,0)
  const calculateCoordinate = (row: number, col: number): string => {
    const centerRow = 16; // 33行的中心是第16行 (0-indexed)
    const centerCol = 16; // 33列的中心是第16列 (0-indexed)
    const x = col - centerCol;
    const y = centerRow - row; // Y轴向上为正
    return `${x},${y}`;
  };

  return (
    <div
      style={{
        // 容器宽度: componetA1容器宽度99%
        width: '99%',
        // 容器高度: componetA1容器高度99%
        height: '99%',
        // 网格排列: 33行 x 33列
        display: 'grid',
        gridTemplateRows: 'repeat(33, 1fr)',
        gridTemplateColumns: 'repeat(33, 1fr)',
        // 组件间隔: 5px
        gap: '5px',
        // 确保网格容器不会溢出
        overflow: 'hidden',
      }}
    >
      {/* 生成33x33的网格组件 */}
      {Array.from({ length: 33 }, (_, row) =>
        Array.from({ length: 33 }, (_, col) => {
          const isHovered = hoveredComponent?.row === row && hoveredComponent?.col === col;
          const coordinateText = calculateCoordinate(row, col);
          
          return (
            <div
              key={`${row}-${col}`}
              style={{
                // 鼠标悬停效果
                transform: isHovered ? 'scale(1.2)' : 'scale(1)',
                zIndex: isHovered ? 10 : 1,
                transition: 'transform 0.2s ease, z-index 0.2s ease',
              }}
            >
              <FeatureA1
                isActive={componentStates[row]?.[col]?.isActive || false}
                hasHighlight={componentStates[row]?.[col]?.hasHighlight || false}
                coordinateText={coordinateText}
                showCoordinates={showCoordinates}
                onClick={() => onComponentClick(row, col)}
                onMouseEnter={() => onComponentMouseEnter(row, col)}
                onMouseLeave={onComponentMouseLeave}
              />
            </div>
          );
        })
      )}
    </div>
  );
};

export default FeatureA1Layout;
