import { create } from 'zustand';

// 组件坐标接口
interface ComponentCoordinate {
  row: number;
  col: number;
}

// 统一状态管理接口
interface StoreState {
  // 模式按键状态 (true/false)
  modeButtonState: boolean;

  // 坐标按键状态 (true/false)
  coordinateButtonState: boolean;

  // 初始化按键时间戳 (一次性事件)
  initializeButtonTimestamp: number;

  // 被激活的组件坐标数组
  activatedComponentCoordinates: ComponentCoordinate[];

  // 设置模式按键状态
  setModeButtonState: (state: boolean) => void;

  // 切换模式按键状态
  toggleModeButton: () => void;

  // 设置坐标按键状态
  setCoordinateButtonState: (state: boolean) => void;

  // 切换坐标按键状态
  toggleCoordinateButton: () => void;

  // 触发初始化事件
  triggerInitialization: () => void;

  // 设置被激活的组件坐标
  setActivatedComponentCoordinates: (coordinates: ComponentCoordinate[]) => void;

  // 添加激活的组件坐标
  addActivatedComponentCoordinate: (coordinate: ComponentCoordinate) => void;

  // 移除激活的组件坐标
  removeActivatedComponentCoordinate: (coordinate: ComponentCoordinate) => void;
}

// Zustand 统一状态管理
export const useStore = create<StoreState>((set) => ({
  // 默认状态
  modeButtonState: false, // 默认模式按键为false
  coordinateButtonState: false, // 默认坐标按键为false
  initializeButtonTimestamp: 0, // 初始化时间戳
  activatedComponentCoordinates: [], // 被激活的组件坐标数组

  // 设置模式按键状态
  setModeButtonState: (state: boolean) => set(() => ({
    modeButtonState: state
  })),

  // 切换模式按键状态
  toggleModeButton: () => set((state) => ({
    modeButtonState: !state.modeButtonState
  })),

  // 设置坐标按键状态
  setCoordinateButtonState: (state: boolean) => set(() => ({
    coordinateButtonState: state
  })),

  // 切换坐标按键状态
  toggleCoordinateButton: () => set((state) => ({
    coordinateButtonState: !state.coordinateButtonState
  })),

  // 触发初始化事件
  triggerInitialization: () => set(() => ({
    initializeButtonTimestamp: Date.now(), // 更新时间戳触发初始化
    activatedComponentCoordinates: [] // 清空激活的组件坐标
  })),

  // 设置被激活的组件坐标
  setActivatedComponentCoordinates: (coordinates: ComponentCoordinate[]) => set(() => ({
    activatedComponentCoordinates: coordinates
  })),

  // 添加激活的组件坐标
  addActivatedComponentCoordinate: (coordinate: ComponentCoordinate) => set((state) => {
    const exists = state.activatedComponentCoordinates.some(
      coord => coord.row === coordinate.row && coord.col === coordinate.col
    );
    if (!exists) {
      return {
        activatedComponentCoordinates: [...state.activatedComponentCoordinates, coordinate]
      };
    }
    return state;
  }),

  // 移除激活的组件坐标
  removeActivatedComponentCoordinate: (coordinate: ComponentCoordinate) => set((state) => ({
    activatedComponentCoordinates: state.activatedComponentCoordinates.filter(
      coord => !(coord.row === coordinate.row && coord.col === coordinate.col)
    )
  }))
}));
