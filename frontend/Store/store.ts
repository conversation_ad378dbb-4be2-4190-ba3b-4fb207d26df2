import { create } from 'zustand';

// 按键状态接口
interface ButtonState {
  modeButtonActive: boolean;
  businessButtonActive: boolean;
  setModeButtonActive: (active: boolean) => void;
  setBusinessButtonActive: (active: boolean) => void;
  toggleModeButton: () => void;
  toggleBusinessButton: () => void;
}

// 矩阵状态接口
interface MatrixState { // 坐标按键状态
  coordinateButtonActive: boolean; // 坐标按键状态
  initializationTimestamp: number; // 初始化时间戳
  modeActive: boolean; // 模式按键状态监听
  setCoordinateButtonActive: (active: boolean) => void;
  toggleCoordinateButton: () => void;
  triggerInitialization: () => void;
  setModeActive: (active: boolean) => void;
}

// 矩阵组件状态接口
interface MatrixComponentState {
  components: { [key: string]: { activated: boolean; highlighted: boolean } };
  setComponentActivated: (id: string, activated: boolean) => void;
  setComponentHighlighted: (id: string) => void;
  clearAllHighlights: () => void;
  resetAllComponents: () => void;
}

// Zustand 状态管理
export const useButtonStore = create<ButtonState>((set) => ({
  // 默认状态：模式按键激活，业务按键未激活
  modeButtonActive: true,
  businessButtonActive: false,

  // 设置模式按键状态
  setModeButtonActive: (active: boolean) => set((state) => ({
    modeButtonActive: active,
    businessButtonActive: active ? false : state.businessButtonActive
  })),

  // 设置业务按键状态
  setBusinessButtonActive: (active: boolean) => set((state) => ({
    businessButtonActive: active,
    modeButtonActive: active ? false : state.modeButtonActive
  })),

  // 切换模式按键（点击时激活模式，禁用业务）
  toggleModeButton: () => set(() => ({
    modeButtonActive: true,
    businessButtonActive: false
  })),

  // 切换业务按键（点击时激活业务，禁用模式）
  toggleBusinessButton: () => set(() => ({
    modeButtonActive: false,
    businessButtonActive: true
  }))
}));

// 矩阵状态管理
export const useMatrixStore = create<MatrixState>((set) => ({
  // 默认状态：坐标按键未激活，模式未激活
  coordinateButtonActive: false,
  initializationTimestamp: 0,
  modeActive: false,

  // 设置坐标按键状态
  setCoordinateButtonActive: (active: boolean) => set(() => ({
    coordinateButtonActive: active
  })),

  // 切换坐标按键状态
  toggleCoordinateButton: () => set((state) => ({
    coordinateButtonActive: !state.coordinateButtonActive
  })),

  // 触发初始化事件
  triggerInitialization: () => set(() => ({
    coordinateButtonActive: false, // 重置坐标按键状态为false
    initializationTimestamp: Date.now() // 更新时间戳
  })),

  // 设置模式状态
  setModeActive: (active: boolean) => set(() => ({
    modeActive: active
  }))
}));

// 矩阵组件状态管理
export const useMatrixComponentStore = create<MatrixComponentState>((set) => ({
  components: {},

  // 设置组件激活状态
  setComponentActivated: (id: string, activated: boolean) => set((state) => ({
    components: {
      ...state.components,
      [id]: {
        ...state.components[id],
        activated
      }
    }
  })),

  // 设置组件高亮状态（互斥单选）
  setComponentHighlighted: (id: string) => set((state) => {
    const newComponents = { ...state.components };
    // 清除所有高亮
    Object.keys(newComponents).forEach(key => {
      if (newComponents[key]) {
        newComponents[key] = { ...newComponents[key], highlighted: false };
      }
    });
    // 设置当前组件高亮
    newComponents[id] = {
      ...newComponents[id],
      highlighted: true
    };
    return { components: newComponents };
  }),

  // 清除所有高亮
  clearAllHighlights: () => set((state) => {
    const newComponents = { ...state.components };
    Object.keys(newComponents).forEach(key => {
      if (newComponents[key]) {
        newComponents[key] = { ...newComponents[key], highlighted: false };
      }
    });
    return { components: newComponents };
  }),

  // 重置所有组件状态
  resetAllComponents: () => set((state) => {
    const newComponents = { ...state.components };
    Object.keys(newComponents).forEach(key => {
      newComponents[key] = { activated: false, highlighted: false };
    });
    return { components: newComponents };
  })
}));
