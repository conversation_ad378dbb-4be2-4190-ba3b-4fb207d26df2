// Zustand 状态管理文件
// 此文件用于管理应用的全局状态

import { create } from 'zustand';

interface ContainerState {
  activeContainer: 'mode' | 'business';
  setActiveContainer: (container: 'mode' | 'business') => void;
}

export const useContainerStore = create<ContainerState>((set) => ({
  activeContainer: 'mode', // 默认显示模式容器
  setActiveContainer: (container) => set({ activeContainer: container }),
}));

// 矩阵功能状态管理
interface MatrixState {
  coordinateButtonState: boolean;
  initializationTimestamp: number;
  setCoordinateButtonState: (state: boolean) => void;
  triggerInitialization: () => void;
}

export const useMatrixStore = create<MatrixState>((set) => ({
  coordinateButtonState: false, // 坐标按键的状态
  initializationTimestamp: 0, // 初始化时间戳
  setCoordinateButtonState: (state) => set({ coordinateButtonState: state }),
  triggerInitialization: () => set({
    initializationTimestamp: Date.now(),
    coordinateButtonState: false // 初始化时重置坐标按键状态
  }),
}));
